defmodule Repobot.SourceFiles do
  @moduledoc """
  The SourceFiles context.
  """
  require Logger

  import Ecto.Query

  alias Repobot.Repo
  alias Repobot.{SourceFile, SourceFileFolder, PullRequest, TemplateContext, Repository}
  alias Repobot.Tags

  @doc """
  Imports a file from a repository and creates a source file.
  The function will:
  1. Create a source file with the given content (or retrieve from repository file if importing from a source repository)
  2. Associate it with the specified repositories
  3. Associate it with the corresponding repository file if importing from a source repository
  4. Infer and assign tags
  5. Return the created source file

  ## Parameters
    * `attrs` - Map of attributes for the source file (must include organization_id and user_id)
    * `repositories` - List of repositories to associate with the source file

  ## Content Handling
  When importing from a source repository (source_repository_id is provided):
  - If content is not provided in attrs, it will be retrieved from the associated repository file
  - The source file will be automatically associated with the repository file
  - No GitHub API calls are made as repository files are kept in sync

  When creating a new source file (no source_repository_id):
  - Content must be provided in attrs
  - No repository file association is created
  """
  def import_file(attrs, repositories) do
    # Ensure required fields exist
    unless attrs[:organization_id] || attrs["organization_id"] do
      raise ArgumentError, "organization_id is required for importing a file"
    end

    unless attrs[:user_id] || attrs["user_id"] do
      raise ArgumentError, "user_id is required for importing a file"
    end

    # Check if we need to associate with a repository file and get content
    attrs_with_repo_file_and_content = maybe_associate_repository_file_and_get_content(attrs)

    with {:ok, source_file} <- create_source_file(attrs_with_repo_file_and_content),
         {:ok, source_file_with_repos} <- associate_with_repositories(source_file, repositories) do
      # Try to determine tags, but don't fail if it doesn't work
      case determine_tags(source_file_with_repos) do
        {:ok, tagged_source_file} -> {:ok, tagged_source_file}
        {:error, :tags} -> {:ok, source_file_with_repos}
      end
    else
      {:error, %Ecto.Changeset{} = changeset} ->
        {:error, "Failed to create source file: #{inspect(changeset.errors)}"}

      {:error, :association} ->
        {:error, "Failed to associate file with repositories"}

      {:error, reason} when is_binary(reason) ->
        {:error, reason}

      {:error, reason} ->
        {:error, "Import failed: #{inspect(reason)}"}
    end
  end

  # Helper function to associate a source file with its corresponding repository file
  # and get content from the repository file if it's being imported from a source repository
  defp maybe_associate_repository_file_and_get_content(attrs) do
    source_repository_id = attrs[:source_repository_id] || attrs["source_repository_id"]
    file_name = attrs[:name] || attrs["name"]
    target_path = attrs[:target_path] || attrs["target_path"]
    content = attrs[:content] || attrs["content"]

    if source_repository_id && file_name do
      # Find the repository file that corresponds to this source file
      # First try by file name (for template files like config.ex.liquid)
      # Then try by target_path (for files with different names like .github/workflows/ci.yml)
      repository_file =
        Repobot.RepositoryFiles.get_repository_file_by_path(source_repository_id, file_name) ||
          (target_path &&
             Repobot.RepositoryFiles.get_repository_file_by_path(
               source_repository_id,
               target_path
             ))

      if repository_file do
        attrs_with_repo_file = Map.put(attrs, :repository_file_id, repository_file.id)

        # If no content is provided, use the content from the repository file
        if is_nil(content) || content == "" do
          Map.put(attrs_with_repo_file, :content, repository_file.content)
        else
          attrs_with_repo_file
        end
      else
        attrs
      end
    else
      attrs
    end
  end

  defp associate_with_repositories(source_file, repositories) do
    # Add foreign key constraint to handle invalid repositories gracefully
    results =
      Enum.map(repositories, fn repository ->
        %Repobot.RepositorySourceFile{}
        |> Repobot.RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: source_file.id
        })
        |> Ecto.Changeset.foreign_key_constraint(:repository_id)
        |> Repo.insert()
      end)

    case Enum.split_with(results, fn
           {:ok, _} -> true
           _ -> false
         end) do
      {_successes, []} -> {:ok, source_file}
      {_, _failures} -> {:error, :association}
    end
  end

  @doc """
  Creates pull requests for a source file in all configured repositories.
  Returns a list of tuples with the repository name and the result.
  """
  def create_pull_requests(%SourceFile{} = source_file, _user) do
    Task.async_stream(
      source_file.repositories,
      fn repository ->
        Logger.info("Creating PR for #{repository.full_name}")

        [owner, repo] = String.split(repository.full_name, "/")

        branch_name = "update-#{source_file.name}-#{DateTime.utc_now() |> DateTime.to_unix()}"
        github_client = github_api().client(owner, repo)

        with {:ok, default_branch} <- get_default_branch(github_client, owner, repo),
             # First get the current file content and render the template
             {:ok, current_content, %{"sha" => sha}} <-
               github_api().get_file_content(github_client, owner, repo, source_file.target_path),
             {:ok, rendered_content} <- render_template_for_repository(source_file, repository) do
          # Only create PR if content is different
          if current_content != rendered_content do
            with {:ok, _ref} <-
                   create_branch(github_client, owner, repo, branch_name, default_branch),
                 {:ok, _content} <-
                   create_or_update_file(
                     github_client,
                     owner,
                     repo,
                     source_file,
                     branch_name,
                     :update,
                     rendered_content,
                     sha
                   ),
                 {:ok, pr} <-
                   create_pr(github_client, owner, repo, branch_name, default_branch, source_file),
                 {:ok, _pull_request} <-
                   create_pull_request_record(source_file, repository.full_name, branch_name, pr) do
              Logger.info("Successfully created PR for #{repository.full_name}")
              {repository.full_name, {:ok, pr["html_url"]}}
            else
              {:error, reason} = error ->
                Logger.error(
                  "Failed to create PR for #{repository.full_name}: #{inspect(reason)}"
                )

                {repository.full_name, error}
            end
          else
            Logger.info("Skipping PR for #{repository.full_name} - content is identical")
            {repository.full_name, {:ok, "No changes needed - content is identical"}}
          end
        else
          {:error, reason} = error ->
            Logger.error("Failed to create PR for #{repository.full_name}: #{inspect(reason)}")
            {repository.full_name, error}
        end
      end,
      timeout: 30_000
    )
    |> Enum.map(fn {:ok, result} -> result end)
  end

  @doc """
  Installs a source file to repositories where it doesn't exist yet.
  Returns a list of tuples with the repository name and the result.
  """
  def install_file(%SourceFile{} = source_file, _user) do
    Logger.info("Starting installation for #{length(source_file.repositories)} repositories")

    results =
      Task.async_stream(
        source_file.repositories,
        fn repository ->
          Logger.info("Installing to #{repository.full_name}")
          [owner, repo] = String.split(repository.full_name, "/")
          github_client = github_api().client(owner, repo)

          # First check if file exists
          case github_api().get_file_status(github_client, owner, repo, source_file.target_path) do
            {:ok, :exists} ->
              {repository.full_name, {:error, "File already exists"}}

            {:ok, :not_found} ->
              # File doesn't exist, create it
              with {:ok, _content} <-
                     create_or_update_file(github_client, owner, repo, source_file, nil, :create) do
                Logger.info("Successfully installed to #{repository.full_name}")
                {repository.full_name, {:ok, "File installed successfully"}}
              else
                {:error, reason} = error ->
                  Logger.error("Failed to install to #{repository.full_name}: #{inspect(reason)}")
                  {repository.full_name, error}
              end

            {:error, reason} ->
              error = "Failed to check file existence: #{reason}"
              Logger.error("Failed to install to #{repository.full_name}: #{error}")
              {repository.full_name, {:error, error}}
          end
        end,
        timeout: 30_000
      )
      |> Enum.map(fn {:ok, result} -> result end)

    Logger.info("Installation complete. Results: #{inspect(results)}")
    results
  end

  @doc """
  Updates an existing source file in all configured repositories.
  Returns a list of tuples with the repository name and the result.
  """
  def update_file(%SourceFile{} = source_file, _user) do
    Logger.info("Starting update for #{length(source_file.repositories)} repositories")

    results =
      Task.async_stream(
        source_file.repositories,
        fn repository ->
          Logger.info("Updating in #{repository.full_name}")
          [owner, repo] = String.split(repository.full_name, "/")
          github_client = github_api().client(owner, repo)

          # First check if file exists
          case github_api().get_file_status(github_client, owner, repo, source_file.target_path) do
            {:ok, :not_found} ->
              {repository.full_name, {:error, "File does not exist"}}

            {:ok, :exists} ->
              # Get current content and render the template
              with {:ok, current_content, %{"sha" => sha}} <-
                     github_api().get_file_content(
                       github_client,
                       owner,
                       repo,
                       source_file.target_path
                     ),
                   {:ok, rendered_content} <-
                     render_template_for_repository(source_file, repository) do
                # Only update if content is different
                if current_content != rendered_content do
                  # File exists, update it
                  case create_or_update_file(
                         github_client,
                         owner,
                         repo,
                         source_file,
                         nil,
                         :update,
                         rendered_content,
                         sha
                       ) do
                    {:ok, _response} ->
                      Logger.info("Successfully updated in #{repository.full_name}")
                      {repository.full_name, {:ok, "File updated successfully"}}

                    {:error, reason} ->
                      Logger.error(
                        "Failed to update in #{repository.full_name}: #{inspect(reason)}"
                      )

                      {repository.full_name, {:error, reason}}
                  end
                else
                  Logger.info(
                    "Skipping update for #{repository.full_name} - content is identical"
                  )

                  {repository.full_name, {:ok, "No changes needed - content is identical"}}
                end
              else
                {:error, reason} ->
                  error = "Failed to get file content: #{reason}"
                  Logger.error("Failed to update in #{repository.full_name}: #{error}")
                  {repository.full_name, {:error, error}}
              end

            {:error, reason} ->
              error = "Failed to check file existence: #{reason}"
              Logger.error("Failed to update in #{repository.full_name}: #{error}")
              {repository.full_name, {:error, error}}
          end
        end,
        timeout: 30_000
      )
      |> Enum.map(fn {:ok, result} -> result end)

    Logger.info("Update complete. Results: #{inspect(results)}")
    results
  end

  defp create_pull_request_record(source_file, repository, branch_name, pr) do
    attrs = %{
      repository: repository,
      branch_name: branch_name,
      pull_request_number: pr["number"],
      pull_request_url: pr["html_url"],
      source_file_id: source_file.id
    }

    %PullRequest{}
    |> PullRequest.changeset(attrs)
    |> Repo.insert()
  end

  defp get_default_branch(client, owner, repo) do
    case github_api().get_repo(client, owner, repo) do
      {200, repo_info, _} -> {:ok, repo_info["default_branch"]}
      {status, body, _} -> {:error, "Failed to get repository info: #{status} - #{inspect(body)}"}
    end
  end

  defp create_branch(client, owner, repo, branch_name, default_branch) do
    # First get the reference of the default branch
    case github_api().get_ref(client, owner, repo, "heads/#{default_branch}") do
      {200, %{"object" => %{"sha" => sha}}, _} ->
        # Create a new reference (branch) using the SHA
        ref = "refs/heads/#{branch_name}"

        case github_api().create_ref(client, owner, repo, ref, sha) do
          {201, _response, _} -> {:ok, ref}
          {status, body, _} -> {:error, "Failed to create branch: #{status} - #{inspect(body)}"}
        end

      {status, body, _} ->
        {:error, "Failed to get default branch ref: #{status} - #{inspect(body)}"}
    end
  end

  defp create_or_update_file(
         client,
         owner,
         repo,
         source_file,
         branch_name,
         operation,
         content \\ nil,
         sha \\ nil
       ) do
    # Get repository data from our database
    with %Repository{data: repo_data, settings: repo_settings, folder_id: folder_id} = _repository <-
           Repo.get_by(Repository, owner: owner, name: repo) do
      # Get folder settings if repository belongs to a folder
      folder_settings =
        if folder_id do
          Repo.get!(Repobot.Folder, folder_id).settings
        else
          %{}
        end

      # Merge settings: folder_settings as base, repo_settings override them
      # Convert all keys to strings to ensure they work in Liquid templates
      settings =
        folder_settings
        |> Map.merge(repo_settings)
        |> stringify_keys()

      # Merge repository data with settings for template variables
      template_vars =
        Map.merge(
          TemplateContext.format_repository_data(repo_data),
          %{"settings" => settings}
        )

      content_to_use =
        case content do
          nil -> render_template(source_file, template_vars)
          {:ok, content} -> {:ok, content}
          content when is_binary(content) -> {:ok, content}
        end

      case content_to_use do
        {:ok, rendered_content} ->
          case operation do
            :create ->
              # Create new file
              message = "Create #{source_file.name} via Repobot"
              content = Base.encode64(rendered_content)

              case github_api().create_file(
                     client,
                     owner,
                     repo,
                     source_file.target_path,
                     content,
                     message,
                     branch_name
                   ) do
                {201, response, _} ->
                  {:ok, response}

                {status, body, _} ->
                  {:error, "Failed to create file: #{status} - #{inspect(body)}"}
              end

            :update ->
              if sha do
                # Use provided SHA
                message = "Update #{source_file.name} via Repobot"
                content = Base.encode64(rendered_content)

                case github_api().update_file(
                       client,
                       owner,
                       repo,
                       source_file.target_path,
                       content,
                       message,
                       sha,
                       branch_name
                     ) do
                  {200, response, _} ->
                    {:ok, response}

                  {status, body, _} ->
                    {:error, "Failed to update file: #{status} - #{inspect(body)}"}
                end
              else
                # Get current file to get its SHA
                case github_api().get_file_content(client, owner, repo, source_file.target_path) do
                  {:ok, _content, %{"sha" => file_sha}} ->
                    message = "Update #{source_file.name} via Repobot"
                    content = Base.encode64(rendered_content)

                    case github_api().update_file(
                           client,
                           owner,
                           repo,
                           source_file.target_path,
                           content,
                           message,
                           file_sha,
                           branch_name
                         ) do
                      {200, response, _} ->
                        {:ok, response}

                      {status, body, _} ->
                        {:error, "Failed to update file: #{status} - #{inspect(body)}"}
                    end

                  {:error, reason} ->
                    {:error, "Failed to get file SHA: #{reason}"}
                end
              end
          end

        {:error, reason} when is_binary(reason) ->
          {:error, reason}

        {:error, reason} ->
          {:error, "Failed to render template: #{inspect(reason)}"}
      end
    end
  end

  defp create_pr(client, owner, repo, branch_name, default_branch, source_file) do
    title = "Update #{source_file.name}"

    body = """
    This PR was automatically created by Repobot to update `#{source_file.name}`.

    The file content is managed centrally and synchronized across repositories.
    """

    case github_api().create_pull_request(
           client,
           owner,
           repo,
           title,
           body,
           branch_name,
           default_branch
         ) do
      {201, response, _} -> {:ok, response}
      {status, body, _} -> {:error, "Failed to create PR: #{status} - #{inspect(body)}"}
    end
  end

  @doc """
  Returns the list of source files for a given user with their pull requests.
  """
  def list_source_files(user) do
    list_source_files(user, Repo.preload(user, :default_organization).default_organization)
  end

  def list_source_files(user, organization) do
    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.user_id == ^user.id and sf.organization_id == ^organization.id,
      select: %{sf | content: fc.blob},
      preload: [:pull_requests, :repositories, :tags, :category, :folders, :source_repository],
      order_by: [desc: sf.updated_at]
    )
    |> Repo.all()
  end

  def list_source_files_for_organization(organization) do
    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.organization_id == ^organization.id,
      select: %{sf | content: fc.blob},
      preload: [:repositories, :source_repository]
    )
    |> Repo.all()
  end

  @doc """
  Returns the list of source files created *from* a specific repository.
  """
  def list_source_files_created_from_repository(repository) do
    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.source_repository_id == ^repository.id,
      select: %{sf | content: fc.blob},
      order_by: [asc: sf.name]
    )
    |> Repo.all()
  end

  @doc """
  Returns the list of source files matching the given IDs.
  """
  def list_source_files_by_ids(ids) do
    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.id in ^ids,
      select: %{sf | content: fc.blob}
    )
    |> Repo.all()
  end

  @doc """
  Gets a single source file with its pull requests.
  Returns nil if the Source file does not exist.
  """
  def get_source_file!(id) do
    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.id == ^id,
      select: %{sf | content: fc.blob},
      preload: [:pull_requests, :repositories, :tags, :category]
    )
    |> Repo.one!()
  end

  def get_source_file!(id, organization_id) do
    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.id == ^id and sf.organization_id == ^organization_id,
      select: %{sf | content: fc.blob},
      preload: [:pull_requests, :repositories, :tags, :category, :folders]
    )
    |> Repo.one!()
  end

  @doc """
  Creates a source file.
  """
  def create_source_file(attrs \\ %{}) do
    {tag_names, attrs} = Map.pop(attrs, "tags", [])
    {content, attrs} = Map.pop(attrs, :content, Map.get(attrs, "content"))

    # Use a transaction to ensure both SourceFile and FileContent are created together
    Repo.transaction(fn ->
      # Create the SourceFile first
      case %SourceFile{}
           |> SourceFile.changeset(attrs)
           |> Repo.insert() do
        {:ok, source_file} ->
          # Create FileContent if content is provided
          file_content =
            if content do
              case Repo.insert(%Repobot.FileContent{
                     blob: content,
                     source_file_id: source_file.id
                   }) do
                {:ok, fc} -> fc
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end
            else
              nil
            end

          # Update SourceFile to reference the FileContent if it was created
          source_file =
            if file_content do
              case Repo.update(
                     Ecto.Changeset.change(source_file, file_content_id: file_content.id)
                   ) do
                {:ok, updated_sf} -> updated_sf
                {:error, changeset} -> Repo.rollback({:source_file_update_error, changeset})
              end
            else
              source_file
            end

          # Get or create tags and associate them with the source file
          source_file_preloaded_user = Repo.preload(source_file, :user)
          tags = Tags.get_or_create_tags(tag_names, source_file_preloaded_user.user)

          case source_file
               |> Repo.preload(:tags)
               |> Ecto.Changeset.change()
               |> Ecto.Changeset.put_assoc(:tags, tags)
               |> Repo.update() do
            {:ok, source_file_with_tags} ->
              # Reload with content projection and preload tags
              reloaded_source_file =
                from(sf in SourceFile,
                  left_join: fc in assoc(sf, :file_content),
                  where: sf.id == ^source_file_with_tags.id,
                  select: %{sf | content: fc.blob},
                  preload: [:tags]
                )
                |> Repo.one!()

              Logger.debug(
                "[SourceFiles.create_source_file] Created SourceFile #{reloaded_source_file.id}, Content size: #{byte_size(reloaded_source_file.content || "")}"
              )

              reloaded_source_file

            {:error, changeset} ->
              Repo.rollback({:tags_error, changeset})
          end

        {:error, changeset} ->
          Repo.rollback({:source_file_error, changeset})
      end
    end)
    |> case do
      {:ok, source_file} -> {:ok, source_file}
      {:error, {_error_type, changeset}} -> {:error, changeset}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Updates a source file from a GitHub push event.
  This function allows updating read-only source files when the update comes from
  a push event to the template repository that manages the source file.
  """
  def update_source_file_from_push(%SourceFile{} = source_file, attrs) do
    source_file = Repo.preload(source_file, [:user, :tags, :source_repository, :file_content])
    {content, attrs} = Map.pop(attrs, :content, Map.get(attrs, "content"))

    Repo.transaction(fn ->
      # Handle content update if provided
      updated_attrs =
        if content do
          case source_file.file_content do
            nil ->
              # Create new FileContent if none exists
              case Repo.insert(%Repobot.FileContent{
                     blob: content,
                     source_file_id: source_file.id
                   }) do
                {:ok, file_content} ->
                  # Update source file to reference the new FileContent
                  Map.put(attrs, :file_content_id, file_content.id)

                {:error, changeset} ->
                  Repo.rollback({:file_content_error, changeset})
              end

            file_content ->
              # Update existing FileContent
              case Repo.update(Ecto.Changeset.change(file_content, blob: content)) do
                {:ok, _} -> attrs
                {:error, changeset} -> Repo.rollback({:file_content_update_error, changeset})
              end
          end
        else
          attrs
        end

      # For push events, we allow updating read-only source files since they're managed by GitHub events
      changeset = SourceFile.changeset_for_push(source_file, updated_attrs)

      case Repo.update(changeset) do
        {:ok, updated_source_file} ->
          # Log source file update event
          log_source_file_update(updated_source_file, attrs, "push_event")

          # Reload with content projection
          from(sf in SourceFile,
            left_join: fc in assoc(sf, :file_content),
            where: sf.id == ^updated_source_file.id,
            select: %{sf | content: fc.blob}
          )
          |> Repo.one!()

        {:error, changeset} ->
          Repo.rollback({:source_file_error, changeset})
      end
    end)
    |> case do
      {:ok, source_file} -> {:ok, source_file}
      {:error, {_error_type, changeset}} -> {:error, changeset}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Updates a source file.
  """
  def update_source_file(%SourceFile{} = source_file, attrs) do
    source_file = Repo.preload(source_file, [:user, :tags, :source_repository, :file_content])

    # Prevent updates to read-only source files unless we're only changing the read_only flag itself
    if source_file.read_only do
      changed_fields = Map.keys(attrs) -- ["read_only", :read_only]

      if not Enum.empty?(changed_fields) do
        {:error,
         "Cannot update read-only source file. This file is managed by GitHub events from its template repository."}
      else
        # Only read_only field is being changed, allow the update
        changeset = SourceFile.changeset(source_file, attrs)

        case Repo.update(changeset) do
          {:ok, updated_source_file} ->
            # Log source file update event
            log_source_file_update(updated_source_file, attrs, "manual_update")

            # Reload with content projection
            reloaded_source_file =
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^updated_source_file.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()

            {:ok, reloaded_source_file}

          error ->
            error
        end
      end
    else
      {content, attrs} = Map.pop(attrs, :content, Map.get(attrs, "content"))

      Repo.transaction(fn ->
        # Handle content update if provided
        updated_attrs =
          if content do
            case source_file.file_content do
              nil ->
                # Create new FileContent if none exists
                case Repo.insert(%Repobot.FileContent{
                       blob: content,
                       source_file_id: source_file.id
                     }) do
                  {:ok, file_content} ->
                    # Update source file to reference the new FileContent
                    Map.put(attrs, :file_content_id, file_content.id)

                  {:error, changeset} ->
                    Repo.rollback({:file_content_error, changeset})
                end

              file_content ->
                # Update existing FileContent
                case Repo.update(Ecto.Changeset.change(file_content, blob: content)) do
                  {:ok, _} -> attrs
                  {:error, changeset} -> Repo.rollback({:file_content_update_error, changeset})
                end
            end
          else
            attrs
          end

        changeset = SourceFile.changeset(source_file, updated_attrs)

        # Check if we're converting to template and need to rename file in GitHub
        needs_github_rename =
          changeset.changes[:is_template] == true and
            source_file.is_template == false and
            not is_nil(source_file.source_repository_id)

        # Only update tags if they were explicitly provided
        changeset =
          if Map.has_key?(updated_attrs, "tags") do
            tag_names = Map.get(updated_attrs, "tags", [])

            # Ensure user has default_organization preloaded for tag operations
            user_with_org = Repo.preload(source_file.user, :default_organization)

            Ecto.Changeset.put_assoc(
              changeset,
              :tags,
              Tags.get_or_create_tags(tag_names, user_with_org)
            )
          else
            changeset
          end

        case Repo.update(changeset) do
          {:ok, updated_source_file} ->
            if needs_github_rename do
              case rename_file_in_github(source_file, updated_source_file) do
                :ok ->
                  # Log source file update event
                  log_source_file_update(updated_source_file, updated_attrs, "manual_update")

                  # Reload with content projection
                  from(sf in SourceFile,
                    left_join: fc in assoc(sf, :file_content),
                    where: sf.id == ^updated_source_file.id,
                    select: %{sf | content: fc.blob}
                  )
                  |> Repo.one!()

                {:error, reason} ->
                  # Rollback the database change if GitHub operation fails
                  Repo.update(
                    SourceFile.changeset(updated_source_file, %{
                      name: source_file.name,
                      target_path: source_file.target_path,
                      is_template: false
                    })
                  )

                  Repo.rollback({:github_rename_error, reason})
              end
            else
              # Log source file update event
              log_source_file_update(updated_source_file, updated_attrs, "manual_update")

              # Reload with content projection
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^updated_source_file.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()
            end

          {:error, changeset} ->
            Repo.rollback({:source_file_error, changeset})
        end
      end)
      |> case do
        {:ok, source_file} ->
          {:ok, source_file}

        {:error, {error_type, reason}} when error_type == :github_rename_error ->
          {:error, "Failed to rename file in GitHub: #{reason}"}

        {:error, {_error_type, changeset}} ->
          {:error, changeset}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  @doc """
  Deletes a source file.
  """
  def delete_source_file(%SourceFile{} = source_file) do
    Repo.delete(source_file)
  end

  @doc """
  Deletes multiple source files by their IDs.
  Returns {:ok, count} where count is the number of deleted files.
  """
  def delete_source_files(file_ids) when is_list(file_ids) do
    {count, _} =
      from(sf in SourceFile, where: sf.id in ^file_ids)
      |> Repo.delete_all()

    {:ok, count}
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking source file changes.
  """
  def change_source_file(%SourceFile{} = source_file, attrs \\ %{}) do
    SourceFile.changeset(source_file, attrs)
  end

  # Renames a file in GitHub when converting to template.
  # Uses the Git Data API to rename the file in a single commit, preserving Git history.
  defp rename_file_in_github(%SourceFile{} = old_source_file, %SourceFile{} = new_source_file) do
    require Logger

    # Only proceed if we have a source repository
    case old_source_file.source_repository do
      nil ->
        Logger.warning("No source repository found for source file #{old_source_file.id}")
        :ok

      source_repo ->
        [owner, repo] = String.split(source_repo.full_name, "/")
        github_client = github_api().client(owner, repo)

        Logger.info("Renaming file in GitHub: #{old_source_file.name} -> #{new_source_file.name}")

        # Get the current file content
        case github_api().get_file_content(github_client, owner, repo, old_source_file.name) do
          {:ok, content, _file_info} ->
            # Use the new rename_file function that uses Git Data API
            commit_message =
              "Convert #{old_source_file.name} to template (rename to #{new_source_file.name})"

            case github_api().rename_file(
                   github_client,
                   owner,
                   repo,
                   old_source_file.name,
                   new_source_file.name,
                   content,
                   commit_message
                 ) do
              {:ok, _commit_sha} ->
                Logger.info("Successfully renamed file in GitHub using Git Data API")
                :ok

              {:error, reason} ->
                Logger.error("Failed to rename file using Git Data API: #{reason}")
                {:error, "Failed to rename file: #{reason}"}
            end

          {:error, reason} ->
            Logger.error("Failed to get file content: #{inspect(reason)}")
            {:error, "Failed to get file content: #{reason}"}
        end
    end
  end

  @doc """
  Imports file content from a repository.
  Returns {:ok, updated_source_file} on success or {:error, reason} on failure.

  This function now creates shared FileContent when importing from a repository file.
  """
  def import_file_content(%SourceFile{} = source_file, repository, _user) do
    # Find the repository record to get its ID
    repository_record = Repobot.Repositories.by_full_name(repository)

    if repository_record do
      # Find the repository file that corresponds to this source file
      repository_file =
        Repobot.RepositoryFiles.get_repository_file_by_path(
          repository_record.id,
          source_file.name
        )

      if repository_file do
        # Load the repository file with its file content
        repository_file = Repo.preload(repository_file, :file_content)

        case repository_file.file_content do
          nil ->
            {:error, "Repository file has no content: #{source_file.name}"}

          file_content ->
            # Create shared content by updating the FileContent to reference both files
            Repo.transaction(fn ->
              # Update the FileContent to reference the source file (creating shared content)
              case Repo.update(
                     Ecto.Changeset.change(file_content, source_file_id: source_file.id)
                   ) do
                {:ok, _} ->
                  # Update the source file to reference the FileContent and repository file
                  case update_source_file(source_file, %{
                         file_content_id: file_content.id,
                         repository_file_id: repository_file.id
                       }) do
                    {:ok, updated_source_file} -> updated_source_file
                    {:error, reason} -> Repo.rollback(reason)
                  end

                {:error, changeset} ->
                  Repo.rollback({:file_content_error, changeset})
              end
            end)
            |> case do
              {:ok, source_file} -> {:ok, source_file}
              {:error, reason} -> {:error, reason}
            end
        end
      else
        {:error, "Repository file not found: #{source_file.name}"}
      end
    else
      {:error, "Repository not found: #{repository}"}
    end
  end

  @doc """
  Determines and assigns tags for a source file using AI.
  """
  def determine_tags(%SourceFile{} = source_file) do
    source_file =
      source_file
      |> Repo.preload([:user, :organization, :folders, repositories: :folder])

    # Ensure organization settings are preloaded for AI backend selection
    organization = Repo.preload(source_file.organization, :settings)

    case ai(organization).infer_tags(source_file, organization) do
      {:ok, tag_names} ->
        tags = Tags.get_or_create_tags(tag_names, source_file.user)

        source_file
        |> Repo.preload(:tags)
        |> Ecto.Changeset.change()
        |> Ecto.Changeset.put_assoc(:tags, tags)
        |> Repo.update()

      {:error, reason} ->
        Logger.error("Failed to determine tags: #{reason}")
        {:error, :tags}
    end
  end

  @doc """
  Associates a source file with a folder.
  """
  def add_source_file_to_folder(%SourceFile{} = source_file, %Repobot.Folder{} = folder) do
    %SourceFileFolder{}
    |> SourceFileFolder.changeset(%{
      source_file_id: source_file.id,
      folder_id: folder.id
    })
    |> Repo.insert(on_conflict: :nothing)

    {:ok, source_file}
  end

  @doc """
  Removes the association between a source file and a folder.
  """
  def remove_source_file_from_folder(%SourceFile{} = source_file, %Repobot.Folder{} = folder) do
    Repo.delete_all(
      from(sf in SourceFileFolder,
        where: sf.source_file_id == ^source_file.id and sf.folder_id == ^folder.id
      )
    )
  end

  def ai(organization \\ nil) do
    Repobot.AI.backend(organization)
  end

  @doc """
  Renders the template content with the given variables using Liquid templating.
  Variables should be a map with string keys matching the template variables.
  If the source file is not marked as a template, returns the content as-is.
  If the content is nil or empty, returns an empty string.

  ## Examples

      iex> render_template(%SourceFile{content: "Hello {{ name }}!", is_template: true}, %{"name" => "World"})
      {:ok, "Hello World!"}

      iex> render_template(%SourceFile{content: "Hello World!", is_template: false}, %{})
      {:ok, "Hello World!"}

      iex> render_template(%SourceFile{content: nil, is_template: true}, %{})
      {:ok, ""}

  ## Available Variables

  When rendering a template with repository data, the following variables are available:

  Basic Info:
  - `{{ name }}` - Repository name
  - `{{ full_name }}` - Full repository name (owner/name)
  - `{{ description }}` - Repository description
  - `{{ homepage }}` - Homepage URL
  - `{{ language }}` - Primary programming language
  - `{{ default_branch }}` - Default branch name
  - `{{ topics }}` - List of repository topics
  - `{{ license }}` - License name
  - `{{ license_key }}` - License identifier

  Stats:
  - `{{ stars }}` - Number of stargazers
  - `{{ watchers }}` - Number of watchers
  - `{{ forks }}` - Number of forks
  - `{{ open_issues }}` - Number of open issues

  Dates:
  - `{{ created_at }}` - Repository creation date
  - `{{ updated_at }}` - Last update date
  - `{{ pushed_at }}` - Last push date

  URLs:
  - `{{ html_url }}` - GitHub repository URL
  - `{{ clone_url }}` - HTTPS clone URL
  - `{{ git_url }}` - Git protocol URL
  - `{{ ssh_url }}` - SSH clone URL

  Owner Info:
  - `{{ owner }}` - Repository owner username
  - `{{ owner_name }}` - Repository owner username (alias)
  - `{{ owner_type }}` - Owner type (User/Organization)
  - `{{ owner_url }}` - Owner's GitHub profile URL
  - `{{ owner_avatar }}` - Owner's avatar URL

  Repository Flags:
  - `{{ private }}` - Whether the repository is private
  - `{{ fork }}` - Whether the repository is a fork
  - `{{ archived }}` - Whether the repository is archived
  - `{{ disabled }}` - Whether the repository is disabled
  - `{{ has_wiki }}` - Whether the repository has a wiki
  - `{{ has_pages }}` - Whether the repository has GitHub Pages
  - `{{ has_issues }}` - Whether the repository has issues enabled
  - `{{ has_projects }}` - Whether the repository has projects enabled
  - `{{ has_downloads }}` - Whether the repository has downloads enabled
  - `{{ has_discussions }}` - Whether the repository has discussions enabled
  """
  def render_template(%SourceFile{content: nil}, _vars), do: {:ok, ""}
  def render_template(%SourceFile{content: "", is_template: _}, _vars), do: {:ok, ""}

  def render_template(%SourceFile{is_template: false} = source_file, _vars) do
    {:ok, source_file.content}
  end

  def render_template(%SourceFile{is_template: true} = source_file, vars) do
    require Logger
    Logger.info("Rendering template with vars: #{inspect(vars)}")

    # Escape GitHub Actions syntax by escaping the $ character
    content = escape_github_actions_syntax(source_file.content)
    Logger.info("Content with escaped GitHub Actions syntax: #{content}")

    case Solid.parse(content) do
      {:ok, template} ->
        # Convert vars to string keys if they aren't already
        string_vars = stringify_keys(vars)
        Logger.info("Parsed template with string vars: #{inspect(string_vars)}")

        case Solid.render(template, string_vars) do
          {:ok, rendered, errors} when is_list(rendered) ->
            # Log any rendering errors but continue with the result
            if not Enum.empty?(errors) do
              Logger.warning("Template rendering warnings: #{inspect(errors)}")
            end

            # Join the rendered parts into a single string and restore GitHub Actions syntax
            result =
              rendered
              |> List.flatten()
              |> Enum.join("")
              |> restore_github_actions_syntax()

            Logger.info("Successfully rendered template: #{result}")
            {:ok, result}

          {:ok, rendered, errors} when is_binary(rendered) ->
            # Log any rendering errors but continue with the result
            if not Enum.empty?(errors) do
              Logger.warning("Template rendering warnings: #{inspect(errors)}")
            end

            result = restore_github_actions_syntax(rendered)
            Logger.info("Successfully rendered template: #{result}")
            {:ok, result}

          {:error, errors, _partial_result} ->
            Logger.error("Template rendering failed: #{inspect(errors)}")
            {:error, "Template rendering failed: #{inspect(errors)}"}
        end

      {:error, reason} ->
        Logger.error("Template parsing failed: #{inspect(reason)}")
        {:error, "Template parsing failed: #{inspect(reason)}"}
    end
  end

  # Escape GitHub Actions syntax by escaping the $ character
  defp escape_github_actions_syntax(content) do
    String.replace(content, "${{", "\\${\\{")
  end

  # Restore GitHub Actions syntax by removing the escaping
  defp restore_github_actions_syntax(content) do
    String.replace(content, "\\${\\{", "${{")
  end

  # Convert all map keys to strings recursively while preserving nested structure
  defp stringify_keys(map) when is_map(map) do
    Map.new(map, fn {k, v} -> {to_string(k), stringify_keys(v)} end)
  end

  defp stringify_keys(list) when is_list(list) do
    Enum.map(list, &stringify_keys/1)
  end

  defp stringify_keys(value), do: value

  defp github_api do
    Application.get_env(:repobot, :github_api)
  end

  @doc """
  Finds source files by source repository ID and target paths.

  Supports preloading associations.
  """
  def find_by_repository_and_paths(source_repository_id, target_paths, opts \\ []) do
    preload = Keyword.get(opts, :preload, [])

    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where: sf.source_repository_id == ^source_repository_id and sf.target_path in ^target_paths,
      select: %{sf | content: fc.blob}
    )
    |> Repo.all()
    |> Repo.preload(preload)
  end

  @doc """
  Finds source files by source repository ID and repository file paths.

  This function handles both template and non-template files:
  - For template files (ending in .liquid), it looks for source files where target_path matches the path without .liquid
  - For non-template files, it looks for source files where target_path matches the path exactly

  Supports preloading associations.
  """
  def find_by_repository_and_file_paths(source_repository_id, file_paths, opts \\ []) do
    preload = Keyword.get(opts, :preload, [])

    # Split paths into template and non-template
    {template_paths, regular_paths} =
      Enum.split_with(file_paths, &String.ends_with?(&1, ".liquid"))

    # For template paths, remove .liquid extension to get target paths
    template_target_paths = Enum.map(template_paths, &String.replace_suffix(&1, ".liquid", ""))

    # Combine all target paths to search for
    all_target_paths = template_target_paths ++ regular_paths

    from(sf in SourceFile,
      left_join: fc in assoc(sf, :file_content),
      where:
        sf.source_repository_id == ^source_repository_id and sf.target_path in ^all_target_paths,
      select: %{sf | content: fc.blob}
    )
    |> Repo.all()
    |> Repo.preload(preload)
  end

  @doc """
  Renders a template for a specific repository, taking into account folder and repository settings.
  """
  def render_template_for_repository(%SourceFile{} = source_file, %Repository{} = repository) do
    repository = Repo.preload(repository, :folder)
    folder_settings = if repository.folder_id, do: repository.folder.settings, else: %{}

    # Merge settings: folder_settings as base, repo_settings override them
    settings =
      folder_settings
      |> Map.merge(repository.settings)
      |> stringify_keys()

    # Merge repository data with settings for template variables
    # Settings are merged at root level (e.g., {{ project }})
    template_vars =
      TemplateContext.format_repository_data(repository.data)
      |> Map.merge(settings)

    render_template(source_file, template_vars)
  end

  def render_template_for_repository(
        %SourceFile{is_template: true, content: template},
        %Repository{data: repo_data, settings: repo_settings}
      ) do
    template_vars = Map.merge(repo_data || %{}, %{"settings" => repo_settings || %{}})

    case Solid.render(template, template_vars) do
      {:ok, rendered_content, errors} ->
        # Log any rendering errors but continue with the result
        if not Enum.empty?(errors) do
          Logger.warning("Template rendering warnings: #{inspect(errors)}")
        end

        {:ok, rendered_content}

      {:error, errors, _partial_result} ->
        {:error, errors}
    end
  end

  def render_template_for_repository(
        %SourceFile{is_template: false, content: content},
        _repository
      ) do
    {:ok, content}
  end

  defp log_source_file_update(%SourceFile{} = source_file, attrs, update_type) do
    # Only log if significant changes were made
    significant_changes =
      [:content, :name, :target_path, :is_template] |> Enum.any?(&Map.has_key?(attrs, &1))

    if significant_changes do
      payload = %{
        "source_file_id" => source_file.id,
        "organization_id" => source_file.organization_id,
        "name" => source_file.name,
        "target_path" => source_file.target_path,
        "is_template" => source_file.is_template,
        "read_only" => source_file.read_only,
        "update_type" => update_type,
        "changes" => Map.keys(attrs) |> Enum.map(&to_string/1)
      }

      Repobot.Events.log_file_update_event(
        "source_file",
        payload,
        source_file.organization_id,
        source_file.user_id,
        nil
      )
    end
  end
end
